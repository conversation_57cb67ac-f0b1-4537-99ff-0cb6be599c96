#!/usr/bin/env python3
"""
Shastry-Sutherland模型序参量分析程序

该程序用于：
1. 从results目录结构中提取分析数据
2. 调用order_calculations.py中的函数计算序参量和关联比
3. 创建各种图表并保存到notebooks目录
4. 支持conda netket环境调试

作者：AI助手
日期：2025年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import pickle
from pathlib import Path
import re
from collections import defaultdict

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 添加当前目录到Python路径以导入order_calculations
sys.path.append('.')
try:
    import order_calculations as oc
except ImportError:
    print("错误：无法导入order_calculations模块。请确保order_calculations.py在当前目录中。")
    sys.exit(1)

class OrderParameterAnalyzer:
    """序参量分析器类"""
    
    def __init__(self, results_dir="../results", output_dir="."):
        self.results_dir = Path(results_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建日志文件
        self.log_file = self.output_dir / "analysis_log.txt"
        
        # 创建plots目录及子文件夹
        self.plots_dir = self.output_dir / "plots"
        self.plots_dir.mkdir(exist_ok=True)
        
        # 创建统一的目录结构
        # Neel (AF) 目录结构
        (self.plots_dir / "neel").mkdir(exist_ok=True)
        (self.plots_dir / "neel" / "order_parameter").mkdir(exist_ok=True)
        (self.plots_dir / "neel" / "ratio").mkdir(exist_ok=True)
        
        # Dimer 目录结构
        (self.plots_dir / "dimer").mkdir(exist_ok=True)
        (self.plots_dir / "dimer" / "order_parameter").mkdir(exist_ok=True)
        (self.plots_dir / "dimer" / "ratio").mkdir(exist_ok=True)
        
        # Diagonal Dimer 目录结构
        (self.plots_dir / "diag_dimer").mkdir(exist_ok=True)
        (self.plots_dir / "diag_dimer" / "order_parameter").mkdir(exist_ok=True)
        (self.plots_dir / "diag_dimer" / "ratio").mkdir(exist_ok=True)
        
        # (self.plots_dir / "plaq_ratio").mkdir(exist_ok=True)  # 暂时注释掉
        
        # 存储分析结果
        self.data = defaultdict(list)
        
        # 初始化日志文件
        self.init_log_file()
        
    def init_log_file(self):
        """初始化日志文件"""
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("Shastry-Sutherland模型序参量分析日志\n")
            f.write("="*80 + "\n\n")
    
    def log_message(self, message):
        """记录日志信息"""
        print(message)  # 同时在控制台显示
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(message + '\n')
    
    def load_data_from_npy(self, data_path):
        """从.npy文件加载数据"""
        try:
            return np.load(data_path, allow_pickle=True).item()
        except Exception as e:
            print(f"加载数据文件 {data_path} 失败: {e}")
            return None
    
    def extract_parameters_from_path(self, path):
        """从路径中提取L, J2, J1参数"""
        path_str = str(path)
        
        # 提取L值
        L_match = re.search(r'L=(\d+)', path_str)
        L = int(L_match.group(1)) if L_match else None
        
        # 提取J2值
        J2_match = re.search(r'J2=([0-9.]+)', path_str)
        J2 = float(J2_match.group(1)) if J2_match else None
        
        # 提取J1值
        J1_match = re.search(r'J1=([0-9.]+)', path_str)
        J1 = float(J1_match.group(1)) if J1_match else None
        
        return L, J2, J1
    
    def process_single_case(self, analysis_dir):
        """处理单个分析案例"""
        L, J2, J1 = self.extract_parameters_from_path(analysis_dir)
        if None in [L, J2, J1]:
            self.log_message(f"无法从路径 {analysis_dir} 提取参数")
            return None
        
        self.log_message("="*80)
        self.log_message(f"正在处理: L={L}, J2={J2}, J1={J1}")
        
        results = {
            'L': L, 'J2': J2, 'J1': J1,
            'neel_ratio': 0.0, 
            'dimer_ratio_0pi': 0.0, 'dimer_ratio_pi0': 0.0,
            'dimer_ratio_avg': 0.0,  # 添加平均值
            'diag_dimer_ratio_0pi': 0.0, 'diag_dimer_ratio_pi0': 0.0,  # 对角二聚体
            'diag_dimer_ratio_avg': 0.0,  # 对角二聚体平均值
            # 序参量数据
            'af_order_param': 0.0,  # 反铁磁序参量
            'dimer_order_param': 0.0,  # 二聚体序参量
            'diag_dimer_order_param': 0.0,  # 对角二聚体序参量
            # 'plaq_ratio': 0.0  # 暂时注释掉
        }
        
        # 处理自旋数据
        spin_data_path = analysis_dir / "spin" / "spin_data.npy"
        if spin_data_path.exists():
            spin_data = self.load_data_from_npy(spin_data_path)
            if spin_data:
                try:
                    self.log_message("-"*60)
                    self.log_message("处理自旋数据 (Neel序)...")
                    
                    # 计算自旋关联比
                    k_points_tuple = (spin_data['metadata']['k_grid']['kx'], spin_data['metadata']['k_grid']['ky'])
                    neel_ratio, neel_details = oc.calculate_correlation_ratios(
                        k_points_tuple, spin_data['structure_factor']['values'], 
                        str(analysis_dir), "spin", log_file=str(self.log_file)
                    )
                    results['neel_ratio'] = neel_ratio
                    
                    # 计算反铁磁序参量
                    af_order_param = oc.calculate_af_order_parameter(
                        k_points_tuple, spin_data['structure_factor']['values'], 
                        L, str(analysis_dir), log_file=str(self.log_file)
                    )
                    results['af_order_param'] = af_order_param
                    
                    # 记录详细信息
                    if isinstance(neel_details, tuple) and len(neel_details) >= 3:
                        main_pos, S_main, S_adj = neel_details
                        self.log_message(f"  主峰位置 (π,π): {main_pos}, 主峰值: {S_main:.6f}")
                        self.log_message(f"  相邻点平均值: {S_adj:.6f}")
                        self.log_message(f"  Neel关联比: {neel_ratio:.6f}")
                        self.log_message(f"  AF Order Parameter: {af_order_param:.6f}")
                    
                except Exception as e:
                    self.log_message(f"处理自旋数据时出错: {e}")
        else:
            self.log_message("未找到自旋数据文件")
        
        # 处理二聚体数据
        dimer_data_path = analysis_dir / "dimer" / "dimer_data.npy"
        if dimer_data_path.exists():
            dimer_data = self.load_data_from_npy(dimer_data_path)
            if dimer_data:
                try:
                    self.log_message("-"*60)
                    self.log_message("处理二聚体数据...")
                    
                    # 计算二聚体关联比
                    k_points_tuple = (dimer_data['metadata']['k_grid']['kx'], dimer_data['metadata']['k_grid']['ky'])
                    dimer_result = oc.calculate_correlation_ratios(
                        k_points_tuple, dimer_data['structure_factor']['values'], 
                        str(analysis_dir), "dimer", log_file=str(self.log_file)
                    )
                    # 处理新的返回格式
                    if isinstance(dimer_result, dict):
                        results['dimer_ratio_0pi'] = dimer_result['ratio_0pi']
                        results['dimer_ratio_pi0'] = dimer_result['ratio_pi0']
                        # 计算平均值
                        results['dimer_ratio_avg'] = 0.5 * (dimer_result['ratio_0pi'] + dimer_result['ratio_pi0'])
                        
                        # 记录详细信息
                        self.log_message(f"  S(0,π) = {dimer_result['S_0pi']:.6f}, 相邻点平均: {dimer_result['S_adj_0pi']:.6f}")
                        self.log_message(f"  S(π,0) = {dimer_result['S_pi0']:.6f}, 相邻点平均: {dimer_result['S_adj_pi0']:.6f}")
                        self.log_message(f"  Dimer关联比 (0,π): {dimer_result['ratio_0pi']:.6f}")
                        self.log_message(f"  Dimer关联比 (π,0): {dimer_result['ratio_pi0']:.6f}")
                        self.log_message(f"  Dimer关联比 (平均): {results['dimer_ratio_avg']:.6f}")
                    else:
                        # 兼容旧格式
                        results['dimer_ratio_0pi'] = dimer_result[0] if isinstance(dimer_result, tuple) else dimer_result
                        results['dimer_ratio_pi0'] = dimer_result[0] if isinstance(dimer_result, tuple) else dimer_result
                        results['dimer_ratio_avg'] = results['dimer_ratio_0pi']  # 旧格式时使用同一个值
                        self.log_message(f"  使用旧格式数据，关联比: {results['dimer_ratio_avg']:.6f}")
                    
                    # 计算二聚体序参量
                    dimer_order_param, _ = oc.calculate_dimer_order_parameter(
                        dimer_data['correlations'], L, str(analysis_dir), log_file=str(self.log_file)
                    )
                    results['dimer_order_param'] = dimer_order_param
                    
                except Exception as e:
                    self.log_message(f"处理二聚体数据时出错: {e}")
        else:
            self.log_message("未找到二聚体数据文件")
        
        # 处理对角二聚体数据
        diag_dimer_data_path = analysis_dir / "diag_dimer" / "diag_dimer_data.npy"
        if diag_dimer_data_path.exists():
            diag_dimer_data = self.load_data_from_npy(diag_dimer_data_path)
            if diag_dimer_data:
                try:
                    self.log_message("-"*60)
                    self.log_message("处理对角二聚体数据...")
                    
                    # 计算对角二聚体关联比
                    k_points_tuple = (diag_dimer_data['metadata']['k_grid']['kx'], diag_dimer_data['metadata']['k_grid']['ky'])
                    diag_dimer_result = oc.calculate_correlation_ratios(
                        k_points_tuple, diag_dimer_data['structure_factor']['values'], 
                        str(analysis_dir), "diag_dimer", log_file=str(self.log_file)
                    )
                    # 处理新的返回格式
                    if isinstance(diag_dimer_result, dict):
                        results['diag_dimer_ratio_0pi'] = diag_dimer_result['ratio_0pi']
                        results['diag_dimer_ratio_pi0'] = diag_dimer_result['ratio_pi0']
                        results['diag_dimer_ratio_avg'] = diag_dimer_result['ratio_avg']
                        
                        # 记录详细信息
                        self.log_message(f"  S(0,π) = {diag_dimer_result['S_0pi']:.6f}, 相邻点: {diag_dimer_result['S_adj_0pi']:.6f}")
                        self.log_message(f"  S(π,0) = {diag_dimer_result['S_pi0']:.6f}, 相邻点: {diag_dimer_result['S_adj_pi0']:.6f}")
                        self.log_message(f"  Diag_Dimer关联比 (0,π): {diag_dimer_result['ratio_0pi']:.6f}")
                        self.log_message(f"  Diag_Dimer关联比 (π,0): {diag_dimer_result['ratio_pi0']:.6f}")
                        self.log_message(f"  Diag_Dimer关联比 (平均): {results['diag_dimer_ratio_avg']:.6f}")
                    else:
                        # 兼容旧格式
                        results['diag_dimer_ratio_0pi'] = diag_dimer_result[0] if isinstance(diag_dimer_result, tuple) else diag_dimer_result
                        results['diag_dimer_ratio_pi0'] = diag_dimer_result[0] if isinstance(diag_dimer_result, tuple) else diag_dimer_result
                        results['diag_dimer_ratio_avg'] = results['diag_dimer_ratio_0pi']  # 旧格式时使用同一个值
                        self.log_message(f"  使用旧格式数据，关联比: {results['diag_dimer_ratio_avg']:.6f}")
                    
                    # 计算对角二聚体序参量
                    diag_dimer_order_param, _ = oc.calculate_diag_dimer_order_parameter(
                        diag_dimer_data['correlations'], L, str(analysis_dir), log_file=str(self.log_file)
                    )
                    results['diag_dimer_order_param'] = diag_dimer_order_param
                    
                except Exception as e:
                    self.log_message(f"处理对角二聚体数据时出错: {e}")
        else:
            self.log_message("未找到对角二聚体数据文件")
        
        # # 处理简盘数据 - 暂时注释掉
        # plaq_data_path = analysis_dir / "plaquette" / "plaquette_data.npy"
        # if plaq_data_path.exists():
        #     plaq_data = self.load_data_from_npy(plaq_data_path)
        #     if plaq_data:
        #         try:
        #             # 计算简盘关联比
        #             k_points_tuple = (plaq_data['k_points']['kx'], plaq_data['k_points']['ky'])
        #             plaq_ratio, _ = oc.calculate_correlation_ratios(
        #                 k_points_tuple, plaq_data['structure_factor'], 
        #                 str(analysis_dir), "plaquette", log_file=None
        #             )
        #             results['plaq_ratio'] = plaq_ratio
        #             
        #         except Exception as e:
        #             print(f"处理简盘数据时出错: {e}")
        # else:
        #     print("未找到简盘数据文件")
        
        self.log_message("-"*80)
        self.log_message(f"完成处理: L={L}, J2={J2}, J1={J1}")
        self.log_message(f"结果摘要:")
        self.log_message(f"  Neel ratio: {results['neel_ratio']:.6f}")
        self.log_message(f"  Dimer ratio (0,π): {results['dimer_ratio_0pi']:.6f}")
        self.log_message(f"  Dimer ratio (π,0): {results['dimer_ratio_pi0']:.6f}")
        self.log_message(f"  Dimer ratio (平均): {results['dimer_ratio_avg']:.6f}")
        self.log_message(f"  Diag_Dimer ratio (0,π): {results['diag_dimer_ratio_0pi']:.6f}")
        self.log_message(f"  Diag_Dimer ratio (π,0): {results['diag_dimer_ratio_pi0']:.6f}")
        self.log_message(f"  Diag_Dimer ratio (平均): {results['diag_dimer_ratio_avg']:.6f}")
        self.log_message(f"  AF Order Parameter: {results['af_order_param']:.6f}")
        self.log_message(f"  Dimer Order Parameter: {results['dimer_order_param']:.6f}")
        self.log_message(f"  Diagonal Dimer Order Parameter: {results['diag_dimer_order_param']:.6f}")
        # self.log_message(f"  Plaq ratio: {results['plaq_ratio']:.6f}")
        self.log_message("="*80)
        self.log_message("")
        
        return results
    
    def collect_all_data(self, L_values=[4, 5, 6], J2_values=[0.00, 0.05, 1.00]):
        """收集所有数据"""
        self.log_message("开始收集数据...")
        
        for L in L_values:
            L_dir = self.results_dir / f"L={L}"
            if not L_dir.exists():
                self.log_message(f"目录 {L_dir} 不存在，跳过")
                continue
                
            for J2 in J2_values:
                J2_dir = L_dir / f"J2={J2:.2f}"
                if not J2_dir.exists():
                    self.log_message(f"目录 {J2_dir} 不存在，跳过")
                    continue
                
                # 遍历所有J1目录
                for J1_dir in J2_dir.iterdir():
                    if not J1_dir.is_dir() or not J1_dir.name.startswith("J1="):
                        continue
                    
                    analysis_dir = J1_dir / "analysis"
                    if not analysis_dir.exists():
                        continue
                    
                    # 处理这个案例
                    result = self.process_single_case(analysis_dir)
                    if result:
                        key = (L, J2)
                        self.data[key].append(result)
        
        # 对数据按J1排序
        for key in self.data:
            self.data[key].sort(key=lambda x: x['J1'])
        
        self.log_message(f"数据收集完成，共收集到 {sum(len(v) for v in self.data.values())} 个数据点")
        
    def plot_neel_ratio_vs_J1(self, J2=0.05):
        """绘制Neel ratio随J1的变化"""
        plt.figure(figsize=(10, 6))
        
        colors = ['red', 'blue', 'green']
        markers = ['o', 's', '^']
        
        # 动态获取有数据的L值
        available_L = sorted(set([key[0] for key in self.data.keys() if key[1] == J2]))
        
        for i, L in enumerate(available_L):
            key = (L, J2)
            if key not in self.data or not self.data[key]:
                continue
            
            data_list = self.data[key]
            J1_values = [d['J1'] for d in data_list]
            neel_ratios = [d['neel_ratio'] for d in data_list]
            
            color = colors[i % len(colors)]
            marker = markers[i % len(markers)]
            plt.plot(J1_values, neel_ratios, color=color, marker=marker, 
                    label=f'Neel Ratio (L={L})', linewidth=2, markersize=6)
        
        plt.xlabel('J1', fontsize=14)
        plt.ylabel('Neel Correlation Ratio', fontsize=14)
        plt.title(f'Neel Correlation Ratio vs J1 (J2={J2})', fontsize=16)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图片
        output_path = self.plots_dir / "neel" / "ratio" / f"neel_ratio_vs_J1_J2_{J2:.2f}.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        self.log_message(f"Neel ratio图表已保存到: {output_path}")
        plt.close()
    
    def plot_dimer_ratio_vs_J1(self, J2=0.05):
        """绘制Dimer ratio随J1的变化，使用平均值"""
        plt.figure(figsize=(10, 6))
        
        colors = ['red', 'blue', 'green']
        markers = ['o', 's', '^']
        
        # 动态获取有数据的L值
        available_L = sorted(set([key[0] for key in self.data.keys() if key[1] == J2]))
        
        for i, L in enumerate(available_L):
            key = (L, J2)
            if key not in self.data or not self.data[key]:
                continue
            
            data_list = self.data[key]
            J1_values = [d['J1'] for d in data_list]
            dimer_ratios_avg = [d['dimer_ratio_avg'] for d in data_list]
            
            color = colors[i % len(colors)]
            marker = markers[i % len(markers)]
            plt.plot(J1_values, dimer_ratios_avg, color=color, marker=marker, 
                    label=f'Dimer Ratio (L={L})', linewidth=2, markersize=6)
        
        plt.xlabel('J1', fontsize=14)
        plt.ylabel('Dimer Correlation Ratio', fontsize=14)
        plt.title(f'Dimer Correlation Ratio vs J1 (J2={J2})', fontsize=16)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图片
        output_path = self.plots_dir / "dimer" / "ratio" / f"dimer_ratio_vs_J1_J2_{J2:.2f}.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        self.log_message(f"Dimer ratio图表已保存到: {output_path}")
        plt.close()
    

    
    def plot_diag_dimer_ratio_vs_J1(self, J2=0.05):
        """绘制Diagonal Dimer ratio随J1的变化，使用(0,π)和(π,0)位置的平均值"""
        plt.figure(figsize=(10, 6))
        
        colors = ['red', 'blue', 'green']
        markers = ['o', 's', '^']
        
        # 动态获取有数据的L值
        available_L = sorted(set([key[0] for key in self.data.keys() if key[1] == J2]))
        
        # 绘制平均ratio
        for i, L in enumerate(available_L):
            key = (L, J2)
            if key not in self.data or not self.data[key]:
                continue
            
            data_list = self.data[key]
            J1_values = [d['J1'] for d in data_list]
            diag_dimer_ratios_avg = [d['diag_dimer_ratio_avg'] for d in data_list]
            
            color = colors[i % len(colors)]
            marker = markers[i % len(markers)]
            plt.plot(J1_values, diag_dimer_ratios_avg, color=color, marker=marker, 
                    label=f'Diagonal Dimer Ratio (L={L})', linewidth=2, markersize=6)
        
        plt.xlabel('J1', fontsize=14)
        plt.ylabel('Diagonal Dimer Correlation Ratio', fontsize=14)
        plt.title(f'Diagonal Dimer Correlation Ratio vs J1 (J2={J2})', fontsize=16)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图片到新的目录结构
        output_path = self.plots_dir / "diag_dimer" / "ratio" / f"diag_dimer_ratio_vs_J1_J2_{J2:.2f}.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        self.log_message(f"Diagonal Dimer ratio图表已保存到: {output_path}")
        plt.close()
    
    def plot_af_order_parameter_vs_J1(self, J2=0.05):
        """绘制反铁磁序参量随J1的变化"""
        plt.figure(figsize=(10, 6))
        
        colors = ['red', 'blue', 'green']
        markers = ['o', 's', '^']
        
        # 动态获取有数据的L值
        available_L = sorted(set([key[0] for key in self.data.keys() if key[1] == J2]))
        
        for i, L in enumerate(available_L):
            key = (L, J2)
            if key not in self.data or not self.data[key]:
                continue
            
            data_list = self.data[key]
            J1_values = [d['J1'] for d in data_list]
            af_order_params = [d['af_order_param'] for d in data_list]
            
            color = colors[i % len(colors)]
            marker = markers[i % len(markers)]
            plt.plot(J1_values, af_order_params, color=color, marker=marker, 
                    label=f'AF Order Parameter (L={L})', linewidth=2, markersize=6)
        
        plt.xlabel('J1', fontsize=14)
        plt.ylabel('AF Order Parameter S(π,π)', fontsize=14)
        plt.title(f'AF Order Parameter vs J1 (J2={J2})', fontsize=16)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存到neel/order_parameter目录
        output_path = self.plots_dir / "neel" / "order_parameter" / f"af_order_parameter_vs_J1_J2_{J2:.2f}.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        self.log_message(f"AF Order Parameter chart saved to: {output_path}")
        plt.close()
    
    def plot_dimer_order_parameter_vs_J1(self, J2=0.05):
        """绘制二聚体序参量随J1的变化"""
        plt.figure(figsize=(10, 6))
        
        colors = ['red', 'blue', 'green']
        markers = ['o', 's', '^']
        
        # 动态获取有数据的L值
        available_L = sorted(set([key[0] for key in self.data.keys() if key[1] == J2]))
        
        for i, L in enumerate(available_L):
            key = (L, J2)
            if key not in self.data or not self.data[key]:
                continue
            
            data_list = self.data[key]
            J1_values = [d['J1'] for d in data_list]
            dimer_order_params = [d['dimer_order_param'] for d in data_list]
            
            color = colors[i % len(colors)]
            marker = markers[i % len(markers)]
            plt.plot(J1_values, dimer_order_params, color=color, marker=marker, 
                    label=f'Dimer Order Parameter (L={L})', linewidth=2, markersize=6)
        
        plt.xlabel('J1', fontsize=14)
        plt.ylabel('Dimer Order Parameter D²', fontsize=14)
        plt.title(f'Dimer Order Parameter vs J1 (J2={J2})', fontsize=16)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存到dimer/order_parameter目录
        output_path = self.plots_dir / "dimer" / "order_parameter" / f"dimer_order_parameter_vs_J1_J2_{J2:.2f}.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        self.log_message(f"Dimer Order Parameter chart saved to: {output_path}")
        plt.close()
    
    def plot_diag_dimer_order_parameter_vs_J1(self, J2=0.05):
        """绘制对角二聚体序参量随J1的变化"""
        plt.figure(figsize=(10, 6))
        
        colors = ['red', 'blue', 'green']
        markers = ['o', 's', '^']
        
        # 动态获取有数据的L值
        available_L = sorted(set([key[0] for key in self.data.keys() if key[1] == J2]))
        
        for i, L in enumerate(available_L):
            key = (L, J2)
            if key not in self.data or not self.data[key]:
                continue
            
            data_list = self.data[key]
            J1_values = [d['J1'] for d in data_list]
            diag_dimer_order_params = [d['diag_dimer_order_param'] for d in data_list]
            
            color = colors[i % len(colors)]
            marker = markers[i % len(markers)]
            plt.plot(J1_values, diag_dimer_order_params, color=color, marker=marker, 
                    label=f'Diagonal Dimer Order Parameter (L={L})', linewidth=2, markersize=6)
        
        plt.xlabel('J1', fontsize=14)
        plt.ylabel('Diagonal Dimer Order Parameter DD²', fontsize=14)
        plt.title(f'Diagonal Dimer Order Parameter vs J1 (J2={J2})', fontsize=16)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存到新的diag_dimer/order_parameter目录
        output_path = self.plots_dir / "diag_dimer" / "order_parameter" / f"diag_dimer_order_parameter_vs_J1_J2_{J2:.2f}.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        self.log_message(f"Diagonal Dimer Order Parameter chart saved to: {output_path}")
        plt.close()
    
    def save_data(self, filename="analysis_results.pkl"):
        """保存分析结果到文件"""
        output_path = self.output_dir / filename
        with open(output_path, 'wb') as f:
            pickle.dump(dict(self.data), f)
        self.log_message(f"分析结果已保存到: {output_path}")
    
    def load_data(self, filename="analysis_results.pkl"):
        """从文件加载分析结果"""
        input_path = self.output_dir / filename
        if input_path.exists():
            with open(input_path, 'rb') as f:
                self.data = defaultdict(list, pickle.load(f))
            self.log_message(f"分析结果已从 {input_path} 加载")
            return True
        return False
    
    def print_summary(self):
        """打印数据摘要"""
        self.log_message("\n" + "="*60)
        self.log_message("数据摘要")
        self.log_message("="*60)
        
        for key, data_list in self.data.items():
            L, J2 = key
            self.log_message(f"\nL={L}, J2={J2}: {len(data_list)} 个数据点")
            
            if data_list:
                J1_values = [d['J1'] for d in data_list]
                self.log_message(f"  J1范围: {min(J1_values):.2f} - {max(J1_values):.2f}")
                
                # 计算平均关联比
                avg_neel_ratio = np.mean([d['neel_ratio'] for d in data_list])
                avg_dimer_ratio_0pi = np.mean([d['dimer_ratio_0pi'] for d in data_list])
                avg_dimer_ratio_pi0 = np.mean([d['dimer_ratio_pi0'] for d in data_list])
                avg_dimer_ratio_avg = np.mean([d['dimer_ratio_avg'] for d in data_list])
                avg_diag_dimer_ratio_0pi = np.mean([d['diag_dimer_ratio_0pi'] for d in data_list])
                avg_diag_dimer_ratio_pi0 = np.mean([d['diag_dimer_ratio_pi0'] for d in data_list])
                avg_diag_dimer_ratio_avg = np.mean([d['diag_dimer_ratio_avg'] for d in data_list])
                # avg_plaq_ratio = np.mean([d['plaq_ratio'] for d in data_list])  # 暂时注释掉
                
                self.log_message(f"  平均关联比:")
                self.log_message(f"    Neel: {avg_neel_ratio:.4f}")
                self.log_message(f"    Dimer (0,π): {avg_dimer_ratio_0pi:.4f}")
                self.log_message(f"    Dimer (π,0): {avg_dimer_ratio_pi0:.4f}")
                self.log_message(f"    Dimer (平均): {avg_dimer_ratio_avg:.4f}")
                self.log_message(f"    Diag_Dimer (0,π): {avg_diag_dimer_ratio_0pi:.4f}")
                self.log_message(f"    Diag_Dimer (π,0): {avg_diag_dimer_ratio_pi0:.4f}")
                self.log_message(f"    Diag_Dimer (平均): {avg_diag_dimer_ratio_avg:.4f}")
                # self.log_message(f"    Plaq: {avg_plaq_ratio:.4f}")  # 暂时注释掉
                
                # 计算平均序参量
                avg_af_order_param = np.mean([d['af_order_param'] for d in data_list])
                avg_dimer_order_param = np.mean([d['dimer_order_param'] for d in data_list])
                avg_diag_dimer_order_param = np.mean([d['diag_dimer_order_param'] for d in data_list])
                
                self.log_message(f"  Average Order Parameters:")
                self.log_message(f"    AF Order Parameter: {avg_af_order_param:.6f}")
                self.log_message(f"    Dimer Order Parameter: {avg_dimer_order_param:.6f}")
                self.log_message(f"    Diagonal Dimer Order Parameter: {avg_diag_dimer_order_param:.6f}")
                self.log_message("")

def main():
    """主函数"""
    print("Shastry-Sutherland模型序参量分析程序")
    print("="*50)
    
    # 创建分析器
    analyzer = OrderParameterAnalyzer()
    
    # 尝试加载已有数据
    if not analyzer.load_data():
        analyzer.log_message("未找到已保存的数据，开始重新收集...")
        # 收集数据
        analyzer.collect_all_data()
        # 保存数据
        analyzer.save_data()
    
    # 打印摘要
    analyzer.print_summary()
    
    # 生成图表
    analyzer.log_message("\n开始生成图表...")
    
    # 对不同J2值生成图表
    for J2 in [0.00, 0.05, 1.00]:
        # 检查是否有这个J2值的数据
        has_data = any(key[1] == J2 for key in analyzer.data.keys())
        if has_data:
            analyzer.log_message(f"\n生成J2={J2}的图表...")
            analyzer.plot_neel_ratio_vs_J1(J2)
            analyzer.plot_dimer_ratio_vs_J1(J2)
            analyzer.plot_diag_dimer_ratio_vs_J1(J2)
            analyzer.plot_af_order_parameter_vs_J1(J2)
            analyzer.plot_dimer_order_parameter_vs_J1(J2)
            analyzer.plot_diag_dimer_order_parameter_vs_J1(J2)
    
    analyzer.log_message("\n分析完成！所有图表已保存到notebooks目录。")
    analyzer.log_message(f"详细日志已保存到: {analyzer.log_file}")
    print(f"\n详细日志已保存到: {analyzer.log_file}")  # 这个保留在控制台显示

if __name__ == "__main__":
    main() 